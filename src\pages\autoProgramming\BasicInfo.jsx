import { Checkbox, ConfigProvider, Input, InputNumber } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CustomCollapse } from '../../common/styledComponent';
import _ from 'lodash';


const BasicInfo = (props) => {
  const {
    arrayRow,
    curProduct,
    productName,
    panelWidth,
    panelHeight,
    arrayColumn,
    setProductName,
    setPanelWidth,
    setPanelHeight,
    setArrayRow,
    setArrayColumn,
    autoProgrammingConfigTemplate,
    selectedInspectionTypes,
    setSelectedInspectionTypes,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex p-4 flex-col self-stretch w-full'>
      <div className='flex p-6 flex-col gap-8 self-stretch flex-1'>
        <div className='flex gap-2 flex-col self-stretch'>
          <div className='flex py-2 items-center gap-2.5'>
            <span className='font-source text-[16px] font-normal leading-[normal]'>
              {t('autoProgramming.basicInfo')}
            </span>
          </div>
          <div className='flex gap-2 flex-col self-stretch'>
            <div className='flex gap-2 items-center self-stretch'>
              <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                {t('autoProgramming.productName')}
              </span>
              <Input
                style={{ width: '100%' }}
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
              />
            </div>
            {/* <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('')}
            </span> */}
          </div>
        </div>
        <div className='flex flex-col gap-2 jutify-center self-stretch'>
          <span className='font-source text-[14px] font-semibold leading-[150%]'>
            {t('autoProgramming.boardInfo')}
          </span>
          <div className='flex gap-[48px] self-stretch'>
            {/* <div className='flex flex-col gap-2 flex-1'>
              <div className='flex gap-2 items-center self-stretch'>
                <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                  {t('autoProgramming.panelLength')}
                </span>
                <div className='flex w-6 h-6 justify-center items-center'>
                  <img
                    src='/icn/panelLength_color.svg'
                    className='w-3 h-4'
                    alt='panelLength_color'
                  />
                </div>
                <InputNumber
                  min={1}
                  style={{ width: '50%' }}
                  controls={false}
                  value={panelHeight}
                  onChange={(value) => setPanelHeight(value)}
                />
              </div>
              <div className='flex gap-2 items-center self-stretch'>
                <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                  {t('autoProgramming.panelWidth')}
                </span>
                <div className='flex w-6 h-6 justify-center items-center'>
                  <img
                    src='/icn/panelWidth_color.svg'
                    className='w-3 h-4'
                    alt='panelWidth'
                  />
                </div>
                <InputNumber
                  min={1}
                  style={{ width: '50%' }}
                  controls={false}
                  value={panelWidth}
                  onChange={(value) => setPanelWidth(value)}
                />
              </div>
            </div> */}
            <div className='flex flex-col gap-2 flex-1'>
              <div className='flex gap-2 items-center self-stretch'>
                <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                  {t('autoProgramming.arrayRow')}
                </span>
                <div className='flex w-6 h-6 justify-center items-center'>
                  <img
                    src='/icn/arrayRow_white.svg'
                    className='w-4 h-3'
                    alt='arrayRow'
                  />
                </div>
                <InputNumber
                  min={1}
                  style={{ width: '120px' }}
                  controls={false}
                  value={arrayRow}
                  onChange={(value) => setArrayRow(value)}
                />
              </div>
              <div className='flex gap-2 items-center self-stretch'>
                <span className='w-[142px] font-source text-[14px] font-normal leading-[150%]'>
                  {t('autoProgramming.arrayCol')}
                </span>
                <div className='flex w-6 h-6 justify-center items-center'>
                  <img
                    src='/icn/arrayCol_white.svg'
                    className='w-6 h-6'
                    alt='arrayCol'
                  />
                </div>
                <InputNumber
                  min={1}
                  style={{ width: '120px' }}
                  controls={false}
                  value={arrayColumn}
                  onChange={(value) => setArrayColumn(value)}
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-2 self-stretch flex-1'>
          <div className='flex flex-col items-start gap-1 self-stretch px-0.5 py-2 rounded-sm'>
            <span className='font-source text-base font-normal leading-[150%] tracking-[0.48px]'>
              {t('autoProgramming.allModelledInspectionTypes')}
            </span>
            <span className='text-gray-4 font-source text-sm font-normal leading-[150%]'>
              {t('autoProgramming.wahtAreTheTypes')}
            </span>
          </div>
          <ConfigProvider
            theme={{
              components: {
                Collapse: {
                  headerPadding: '0px 0 0px 8px',
                  contentPadding: '0 0 0 8px',
                }
              }
            }}
          >
            <CustomCollapse
              style={{ width: '100%' }}
              items={_.map(_.keys(_.get(autoProgrammingConfigTemplate, 'component_for_line_item', {})), k => ({
                key: k,
                label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between`}>
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t(`autoProgramming.inspectionTypes.${k}`, k)}
                  </span>
                </div>,
                children: <div className='flex flex-col pl-4'>
                  {_.map(_.get(autoProgrammingConfigTemplate, `component_for_line_item.${k}`, []), (v) => (
                    <div className='flex items-center gap-2' key={v}>
                      <Checkbox
                        size='small'
                        checked={_.includes(_.get(selectedInspectionTypes, k, []), v)}
                        onChange={(e) => {
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          if (e.target.checked) {
                            newInspTypes[k] = [...(newInspTypes[k] || []), v];
                          } else {
                            newInspTypes[k] = _.filter(newInspTypes[k], (t) => t !== v);
                          }
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {/* {v} */}
                        {t(`autoProgramming.componentTypes.${v}`, v)}
                      </span>
                    </div>
                  ))}
                </div>,
              }))}
            />
          </ConfigProvider>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;